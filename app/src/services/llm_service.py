"""
Refactored LLM Service - Main entry point for backward compatibility.

This module provides the main LLMService class that maintains backward compatibility
while using the new modular architecture internally.
"""

from typing import Any, AsyncGenerator, Callable, List, Optional

from app.src.schemas.chat_sessions import ChatHistoryItem
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

# Import the refactored service
from app.src.services.llm.llm_service import LLMService as RefactoredLLMService


class LLMService:
    """
    Backward-compatible wrapper for the refactored LLM service.
    
    This class maintains the same interface as the original LLMService
    while delegating to the new modular implementation.
    """
    
    def __init__(
        self, 
        search_service: Optional[SearchService] = None, 
        prompt_service: Optional[PromptService] = None
    ):
        """
        Initialize the LLM service wrapper.
        
        Args:
            search_service: Search service for web search functionality
            prompt_service: Prompt service for system prompts
        """
        # Delegate to the refactored service
        self._service = RefactoredLLMService(
            search_service=search_service,
            prompt_service=prompt_service
        )
    
    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for the given request and chat history.
        
        This method maintains backward compatibility with the original interface
        while using the new modular implementation.
        
        Args:
            request: User request object
            chat_history: List of previous chat messages
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            on_tool_call: Optional callback for tool call notifications
            
        Yields:
            Streaming response content
        """
        # Delegate to the refactored service
        async for chunk in self._service.generate_stream_response(
            request=request,
            chat_history=chat_history,
            max_tokens=max_tokens,
            temperature=temperature,
            on_tool_call=on_tool_call,
        ):
            yield chunk
    
    def get_available_tools(self) -> List[str]:
        """
        Get list of available tool names.
        
        Returns:
            List of available tool names
        """
        return self._service.get_available_tools()
    
    def get_tool_configs(self) -> List[dict]:
        """
        Get configuration for all available tools.
        
        Returns:
            List of tool configurations
        """
        return self._service.get_tool_configs()
