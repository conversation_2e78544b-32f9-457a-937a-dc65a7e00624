"""
LLM Client wrapper for OpenAI API interactions.

This module provides a clean interface for OpenAI API calls with proper error handling
and configuration management.
"""

from typing import Any, AsyncGenerator, Dict, List, Optional

import decouple
from openai import APIError, AsyncOpenAI, AuthenticationError, RateLimitError

from app.src.exceptions.error_code import ChatbotErrorCode


class LLMClient:
    """
    Wrapper for OpenAI AsyncClient with error handling and configuration.
    
    This class encapsulates OpenAI API interactions and provides a clean interface
    for the rest of the application.
    """
    
    def __init__(self, api_key: Optional[str] = None, model: Optional[str] = None):
        """
        Initialize the LLM client.
        
        Args:
            api_key: OpenAI API key. If None, will be read from environment.
            model: Default model to use. If None, will be read from environment.
        """
        self.api_key = api_key or decouple.config("OPENAI_API_KEY", default=None)
        self.model = model or decouple.config("MODEL", default="gpt-4o-mini")
        self.client = AsyncOpenAI(api_key=self.api_key)
    
    async def create_chat_completion(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: int = 4096,
        temperature: float = 0,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: str = "auto",
        stream: bool = False,
    ) -> Any:
        """
        Create a chat completion with error handling.
        
        Args:
            messages: List of message dictionaries
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            tools: Optional list of tools for function calling
            tool_choice: Tool choice strategy
            stream: Whether to stream the response
            
        Returns:
            OpenAI chat completion response
            
        Raises:
            HTTPException: For various API errors
        """
        try:
            kwargs = {
                "model": self.model,
                "messages": messages,
                "max_tokens": max_tokens,
                "temperature": temperature,
                "stream": stream,
            }
            
            if tools:
                kwargs["tools"] = tools
                kwargs["tool_choice"] = tool_choice
            
            return await self.client.chat.completions.create(**kwargs)
            
        except APIError:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value
        except RateLimitError:
            raise ChatbotErrorCode.REQUEST_DENIED.value
        except AuthenticationError:
            raise ChatbotErrorCode.AUTHENTICATION_FAILED.value
        except Exception:
            raise ChatbotErrorCode.RESPONSE_NOT_AVAILABLE.value
    
    async def create_stream_completion(
        self,
        messages: List[Dict[str, Any]],
        max_tokens: int = 4096,
        temperature: float = 0,
        tools: Optional[List[Dict[str, Any]]] = None,
        tool_choice: str = "auto",
    ) -> AsyncGenerator[Any, None]:
        """
        Create a streaming chat completion.
        
        Args:
            messages: List of message dictionaries
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            tools: Optional list of tools for function calling
            tool_choice: Tool choice strategy
            
        Yields:
            Stream chunks from OpenAI API
        """
        response = await self.create_chat_completion(
            messages=messages,
            max_tokens=max_tokens,
            temperature=temperature,
            tools=tools,
            tool_choice=tool_choice,
            stream=True,
        )
        
        async for chunk in response:
            yield chunk
