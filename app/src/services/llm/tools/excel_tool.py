"""
Excel file creation tool for LLM function calling.

This tool creates downloadable Excel files for advertising campaigns.
"""

from pathlib import Path
from typing import Any, Dict

from app.src.services.llm.excel.excel_manager import ExcelManager
from app.src.services.llm.utils.prompt_generator import PromptGenerator
from app.src.services.prompt_service import PromptService
from .base_tool import BaseTool


class ExcelTool(BaseTool):
    """
    Tool for creating Excel files for advertising campaigns.
    
    This tool handles the complete workflow of Excel file generation
    including data extraction, formatting, and file creation.
    """
    
    def __init__(self, prompt_service: PromptService, llm_client):
        """
        Initialize the Excel tool.
        
        Args:
            prompt_service: Prompt service for system prompts
            llm_client: LLM client for data extraction
        """
        self.prompt_service = prompt_service
        self.llm_client = llm_client
        self.outputs_dir = Path(__file__).parent.parent.parent.parent / "data/outputs"
        self.excel_manager = ExcelManager(self.outputs_dir)
        self.prompt_generator = PromptGenerator()
    
    @property
    def name(self) -> str:
        """Return the tool name."""
        return "create_excel_file"
    
    @property
    def description(self) -> str:
        """Return the tool description."""
        return "Creates a downloadable Excel file for an advertising campaign based on the specified platform."
    
    @property
    def parameters(self) -> Dict[str, Any]:
        """Return the tool parameters schema."""
        return {
            "type": "object",
            "properties": {
                "platform": {
                    "type": "string",
                    "description": (
                        "The advertising platform (e.g., LINE Ads, Meta, YouTube Ads, "
                        "Google Search Ads, Google Display Ads, Google Demand Gen Ads, P-Max)."
                    ),
                    "enum": [
                        "LINE Ads",
                        "Meta (Instagram/Facebook)",
                        "YouTube Ads",
                        "Google Search Ads",
                        "Google Display Ads",
                        "Google Demand Gen Ads",
                        "P-Max"
                    ]
                }
            },
            "required": ["platform"]
        }
    
    async def execute(self, platform: str, **_kwargs) -> Dict[str, Any]:
        """
        Execute Excel file creation.

        Args:
            platform: Advertising platform name
            **kwargs: Additional parameters including conversation context

        Returns:
            Dictionary with download URL
        """
        try:
            # For now, create a simple campaign data structure
            # In a full implementation, this would extract data from conversation context
            campaign_data = {
                "data": {
                    "キャンペーン名": f"{platform}キャンペーン",
                    "広告グループ名": ["グループ1", "グループ2"],
                    "配信条件": ["条件1", "条件2"],
                    "性別": "すべて",
                    "年齢": "18-65",
                    "エリア": "日本全国",
                },
                "missing_fields": []
            }

            # Create Excel file
            download_url = await self.excel_manager.create_excel_file(platform, campaign_data)

            return {"download_url": download_url}

        except ValueError as e:
            raise Exception(str(e))
        except Exception as e:
            raise Exception(f"Excel file creation failed: {str(e)}")
    

