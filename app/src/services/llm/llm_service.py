"""
Refactored LLM Service with improved modularity and separation of concerns.

This service orchestrates LLM operations using a modular architecture with
separate components for client management, stream processing, tool execution,
and file generation.
"""

from typing import Any, AsyncGenerator, Callable, List, Optional

from fastapi import HTTPException

from app.src.exceptions.error_code import <PERSON><PERSON><PERSON><PERSON><PERSON>r<PERSON><PERSON>
from app.src.schemas.chat_sessions import <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>
from app.src.services.prompt_service import PromptService
from app.src.services.search_service import SearchService

from .core.llm_client import LLMClient
from .core.stream_processor import StreamProcessor
from .core.error_handler import ErrorHandler
from .tools.tool_registry import ToolRegistry
from .tools.web_search_tool import WebSearchTool
from .tools.csv_tool import CSVTool
from .tools.excel_tool import ExcelTool


class LLMService:
    """
    Main LLM service that orchestrates all LLM-related operations.
    
    This refactored service uses a modular architecture with clear separation
    of concerns for better maintainability and testability.
    """
    
    def __init__(
        self, 
        search_service: Optional[SearchService] = None, 
        prompt_service: Optional[PromptService] = None
    ):
        """
        Initialize the LLM service with all required components.
        
        Args:
            search_service: Search service for web search functionality
            prompt_service: Prompt service for system prompts
        """
        self.search_service = search_service
        self.prompt_service = prompt_service
        
        # Initialize core components
        self.llm_client = LLMClient()
        self.error_handler = ErrorHandler(self.llm_client)
        
        # Initialize and configure tool registry
        self.tool_registry = ToolRegistry()
        self._register_tools()
        
        # Initialize stream processor
        self.stream_processor = StreamProcessor(
            llm_client=self.llm_client,
            tool_registry=self.tool_registry,
            error_handler=self.error_handler,
        )
    
    def _register_tools(self) -> None:
        """Register all available tools in the tool registry."""
        if self.search_service:
            self.tool_registry.register_tool(WebSearchTool(self.search_service))
        
        self.tool_registry.register_tool(CSVTool())
        
        if self.prompt_service:
            self.tool_registry.register_tool(ExcelTool(self.prompt_service, self.llm_client))
    
    async def generate_stream_response(
        self,
        request: Any,
        chat_history: List[ChatHistoryItem],
        max_tokens: int = 4096,
        temperature: float = 0,
        on_tool_call: Optional[Callable[[str], None]] = None,
    ) -> AsyncGenerator[str, None]:
        """
        Generate a streaming response for the given request and chat history.
        
        Args:
            request: User request object
            chat_history: List of previous chat messages
            max_tokens: Maximum tokens in response
            temperature: Temperature parameter for randomness
            on_tool_call: Optional callback for tool call notifications
            
        Yields:
            Streaming response content
            
        Raises:
            HTTPException: For various API errors
        """
        try:
            # Get system prompt
            system_prompt = await self.prompt_service._get_prompts()
        except ValueError as e:
            raise HTTPException(status_code=400, detail=str(e))
        
        # Prepare messages
        messages = [{"role": "system", "content": system_prompt}]
        
        for item in chat_history:
            messages.append({"role": item.role, "content": item.content})
        messages.append({"role": "user", "content": request.question})
        
        try:
            # Process stream with tool handling
            async for chunk in self.stream_processor.process_stream(
                messages=messages,
                max_tokens=max_tokens,
                temperature=temperature,
                on_tool_call=on_tool_call,
            ):
                yield chunk
                
        except Exception as e:
            # Handle any errors that weren't caught by the stream processor
            if isinstance(e, HTTPException):
                raise e
            
            # Convert other exceptions to appropriate error codes
            error_message = "❌ 処理中にエラーが発生しました。もう一度お試しください。"
            async for chunk in self.error_handler.stream_error_response(error_message):
                yield chunk
    
    def get_available_tools(self) -> List[str]:
        """
        Get list of available tool names.
        
        Returns:
            List of available tool names
        """
        return self.tool_registry.list_tools()
    
    def get_tool_configs(self) -> List[dict]:
        """
        Get configuration for all available tools.
        
        Returns:
            List of tool configurations
        """
        return self.tool_registry.get_tools_config()

    @staticmethod
    def should_update_summary(chat_history: list) -> bool:
        return len(chat_history) == 0 or len(chat_history) % 20 == 0

    @staticmethod
    def extract_questions(chat_history: list, current_question: str) -> list[str]:
        if not chat_history:
            return [current_question]
        questions = [msg.content for i, msg in enumerate(chat_history) if i % 2 == 0]
        questions.append(current_question)
        return questions