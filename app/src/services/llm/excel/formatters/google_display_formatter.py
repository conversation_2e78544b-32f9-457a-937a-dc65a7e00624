"""
Google Display Ads Excel formatter.

This module handles Excel file generation for Google Display advertising campaigns.
"""

from typing import Any, Dict

from ..base_writer import BaseExcelWriter
from ..styles import ExcelStyles, set_cell


class GoogleDisplayFormatter(BaseExcelWriter):
    """
    Excel formatter for Google Display advertising campaigns.
    """
    
    async def write_excel_file(
        self, 
        campaign_data: Dict[str, Any], 
        output_format: Dict[str, Any], 
        file_path: str
    ) -> None:
        """
        Write Excel file for Google Display advertising campaigns.
        
        Args:
            campaign_data: Campaign data dictionary
            output_format: Output format configuration
            file_path: Path to save the Excel file
        """
        wb, ws = self.create_workbook("Googleディスプレイ広告")
        
        # Headers
        headers = [
            "媒体", "キャンペーン名", "広告グループ名", "配信条件", 
            "デバイス", "性別", "年齢", "エリア", "除外プレースメント"
        ]
        
        for idx, header in enumerate(headers, start=1):
            set_cell(ws, 1, idx, header, fill=ExcelStyles.LIGHT_BLUE_FILL, bold=True)
        
        data = campaign_data.get('data', {})
        
        # Non-list fields
        non_list_fields = [
            "媒体", "キャンペーン名", "デバイス", "性別", 
            "年齢", "エリア", "除外プレースメント"
        ]
        for idx, header in enumerate(headers, start=1):
            if header in non_list_fields:
                ws.merge_cells(start_row=2, start_column=idx, end_row=3, end_column=idx)
                value = self.get_field_value(
                    data.get(header, "Googleディスプレイ" if header == "媒体" else "")
                )
                set_cell(ws, 2, idx, value)
        
        # List fields
        list_fields = {"広告グループ名": 2, "配信条件": 2}
        for idx, header in enumerate(headers, start=1):
            if header in list_fields:
                values = self.get_field_value(data.get(header, []))
                values = self.ensure_list(values, max_items=2)
                for row, value in enumerate(values, start=2):
                    set_cell(ws, row, idx, value)
        
        # Link destination
        set_cell(ws, 4, 1, "リンク先", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, 4, 2, "", merge_end_col=9)
        
        # Platform-specific URL
        platform = self.get_field_value(data.get("媒体", "Googleディスプレイ"))
        row = 5
        
        if platform == "Googleディスプレイ":
            google_url = self.get_nested_value(
                data, "広告文.Google.URL", "?utm_source=google&utm_medium=display"
            )
            set_cell(ws, row, 1, "Google URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
            set_cell(ws, row, 2, google_url, merge_end_col=9)
        elif platform == "Yahooディスプレイ":
            yda_url = self.get_nested_value(
                data, "広告文.Yahoo.URL", "?utm_source=yahoo&utm_medium=display"
            )
            set_cell(ws, row, 1, "YDA URL", fill=ExcelStyles.LIGHT_BLUE_FILL)
            set_cell(ws, row, 2, yda_url, merge_end_col=9)
        
        # Ad content section (simplified)
        row += 2
        set_cell(ws, row, 1, f"広告文（{platform}）")
        set_cell(ws, row + 1, 1, "広告種類", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 2, "配信内容", fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 3, "広告文", merge_end_col=7, fill=ExcelStyles.LIGHT_BLUE_FILL)
        set_cell(ws, row + 1, 8, "文字数", fill=ExcelStyles.LIGHT_BLUE_FILL)
        
        # Ad type and content
        ad_type = self.get_field_value(data.get("広告種類", "レスポンシブ ディスプレイ広告"))
        
        content_row = row + 2
        
        # Main advertiser
        advertiser = self.get_nested_value(data, "広告文.Google.主体者表記", "")
        set_cell(ws, content_row, 2, "主体者表記（最大25文字）")
        set_cell(ws, content_row, 3, advertiser, merge_end_col=7)
        set_cell(ws, content_row, 8, len(advertiser))
        
        # Headlines
        headlines = self.get_nested_value(data, "広告文.Google.広告見出し 短縮", [])
        headlines = self.ensure_list(headlines, max_items=5)
        for idx, headline in enumerate(headlines):
            row_num = content_row + 1 + idx
            label = "広告見出し 短縮（最大30文字）" + ("【任意】" if idx > 0 else "")
            set_cell(ws, row_num, 2, label)
            set_cell(ws, row_num, 3, headline, merge_end_col=7)
            set_cell(ws, row_num, 8, len(headline))
        
        # Set ad type with proper merging
        end_row = content_row + len(headlines)
        set_cell(ws, content_row, 1, ad_type, merge_end_row=end_row)
        
        self.save_workbook(wb, file_path)
